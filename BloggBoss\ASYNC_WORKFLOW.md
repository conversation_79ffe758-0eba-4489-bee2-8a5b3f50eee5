# Asynchronous Blog Post Generation - CORRECTED WORKFLOW

## Overview

This plugin now supports asynchronous blog post generation to handle long-running AI processes that exceed nginx timeout limits (typically 60 seconds). **IMPORTANT**: The async workflow sends only ONE request to the AI agent and then monitors completion via chat history, avoiding multiple message confusion.

## How It Works

### Traditional Synchronous Flow (Legacy)
1. User clicks "Generate Post Now (Sync)"
2. <PERSON><PERSON><PERSON> sends request to Flowise agent with user's exact prompt
3. <PERSON><PERSON><PERSON> waits for complete response (may timeout)
4. Creates WordPress post if successful

### New Asynchronous Flow (Recommended) - BACKGROUND PROCESSING
1. User clicks "Start Async Generation"
2. <PERSON>lug<PERSON> creates job and returns immediately with job ID
3. Background process sends **ONE** request to Flowise agent with user's exact prompt
4. Background polling checks chat history for completion (NOT sending new messages)
5. When AI processing is complete, plugin creates WordPress post
6. User can check status at any time via AJAX

## Key Corrections Made

### ❌ Previous Issues:
- Sending multiple requests with different prompts
- Using "status" messages that confused the AI
- Getting wrong responses like "Hey, how are you?"
- Modifying user's original prompt

### ✅ Fixed Approach:
- Return immediately to user with job ID (no frontend timeout)
- Send user's exact prompt only once in background
- Use Flowise chat history API to check completion
- No additional messages sent during polling
- Preserve original prompt without modifications
- Background processing handles all timeouts

## Key Features

### Session-Based Tracking
- Each async job gets a unique job ID
- Flowise session ID is stored for polling
- Job status is tracked in database

### Background Processing
- Uses WordPress cron system for polling
- Automatic timeout after 10 minutes
- Detailed logging of all steps

### Real-Time Status Updates
- AJAX-powered status checking
- Auto-refresh for active jobs
- Direct links to completed posts

## Database Schema

The plugin creates a `wp_ai_blog_jobs` table with the following structure:

```sql
CREATE TABLE wp_ai_blog_jobs (
    id mediumint(9) NOT NULL AUTO_INCREMENT,
    job_id varchar(36) NOT NULL,
    status varchar(50) NOT NULL,
    settings longtext,
    session_data longtext,
    result_data longtext,
    error_message text,
    created_at datetime DEFAULT CURRENT_TIMESTAMP,
    updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    UNIQUE KEY job_id (job_id),
    KEY status (status)
);
```

## Job Statuses

- `initiated`: Job created in database
- `ai_initiating`: Sending request to AI agent in background
- `ai_processing`: Waiting for AI to complete content generation (checking chat history)
- `ai_complete`: AI finished generating content, ready for post creation
- `creating_post`: Creating WordPress post from AI content
- `completed`: Post successfully created
- `failed`: Job failed with error
- `timeout`: Job exceeded maximum processing time (10 minutes)

## API Integration

### Flowise Session Handling - CORRECTED
The plugin works with Flowise's session-based API correctly:

1. **Initial Request**: Sends user's exact prompt to Flowise agent (ONE TIME ONLY)
2. **Session Response**: Receives `sessionId` and `chatId` (may include immediate result)
3. **History Polling**: Uses chat history API to check for completion (NO NEW MESSAGES)
4. **Content Retrieval**: Gets final blog content from chat history when ready

### API Endpoints Used
- **POST** `/api/v1/prediction/{agent_id}` - Send initial prompt
- **GET** `/api/v1/chatmessage/{session_id}` - Check chat history for completion

### Example Flowise Response
```json
{
    "text": "Generated blog content...",
    "question": "Original prompt",
    "chatId": "8811ac47-e0f1-434b-82e8-8a9cc1633c72",
    "chatMessageId": "a49b8386-20fc-4684-834c-44d5976e479a",
    "sessionId": "8811ac47-e0f1-434b-82e8-8a9cc1633c72",
    "agentReasoning": [...]
}
```

## Configuration

### Polling Settings
- **Polling Interval**: 10 seconds between status checks
- **Max Polling Time**: 10 minutes total timeout
- **Retry Attempts**: 3 attempts for failed requests

### Fast Mode Support
Both sync and async modes support the existing "Fast Mode" setting:
- Fast Mode: 300-word limit, 50-second timeout
- Full Mode: Complete prompt, 5-minute timeout

## Usage Instructions

### For Users
1. Go to the plugin settings page
2. Choose "Start Async Generation" for long-running processes
3. Note the Job ID provided
4. Click "Check Status" to monitor progress
5. View completed post when ready

### For Developers
```php
// Start async generation
$result = ai_auto_blog_start_async_generation();
if ($result['status'] === 'success') {
    $job_id = $result['job_id'];
}

// Check job status
$status = ai_auto_blog_get_job_status($job_id);
echo $status['message'];
```

## Troubleshooting

### Common Issues
1. **Nginx Timeout**: Use async mode instead of sync
2. **Job Stuck**: Check logs for AI API errors
3. **Missing Posts**: Verify WordPress API credentials
4. **Database Errors**: Ensure proper table creation

### Logging
All async operations are logged with detailed information:
- Job creation and status updates
- AI API requests and responses
- WordPress post creation
- Error conditions and timeouts

## Benefits

1. **No More Timeouts**: Handles long AI processing times
2. **Better User Experience**: Immediate feedback with progress tracking
3. **Reliable Processing**: Background polling ensures completion
4. **Detailed Monitoring**: Real-time status updates and logging
5. **Backward Compatible**: Original sync mode still available
