<?php
/**
 * Test script for async blog post generation
 * Run this from WordPress admin or via WP-CLI to test the async functionality
 */

// Ensure this is run in WordPress context
if (!defined('ABSPATH')) {
    die('This script must be run in WordPress context');
}

require_once plugin_dir_path(__FILE__) . 'includes/post-handler.php';

echo "<h2>Testing Async Blog Post Generation</h2>\n";

// Test 1: Create jobs table
echo "<h3>Test 1: Database Table Creation</h3>\n";
try {
    AI_Auto_Blog_Poster::create_jobs_table_public();
    echo "✅ Jobs table created successfully<br>\n";
} catch (Exception $e) {
    echo "❌ Failed to create jobs table: " . $e->getMessage() . "<br>\n";
}

// Test 2: Check required settings
echo "<h3>Test 2: Settings Validation</h3>\n";
$required_settings = [
    'ai_auto_blog_site_url',
    'ai_auto_blog_username', 
    'ai_auto_blog_password',
    'ai_auto_blog_category',
    'ai_auto_blog_agent_url',
    'ai_auto_blog_prompt'
];

$missing_settings = [];
foreach ($required_settings as $setting) {
    $value = get_option($setting);
    if (empty($value)) {
        $missing_settings[] = $setting;
    }
}

if (empty($missing_settings)) {
    echo "✅ All required settings are configured<br>\n";
} else {
    echo "⚠️ Missing settings: " . implode(', ', $missing_settings) . "<br>\n";
    echo "Please configure these settings before testing async generation.<br>\n";
}

// Test 3: Test job creation (if settings are available)
if (empty($missing_settings)) {
    echo "<h3>Test 3: Job Creation</h3>\n";
    try {
        $result = ai_auto_blog_start_async_generation();
        if ($result['status'] === 'success') {
            echo "✅ Async job created successfully<br>\n";
            echo "Job ID: " . $result['job_id'] . "<br>\n";
            
            // Test status checking
            echo "<h3>Test 4: Status Checking</h3>\n";
            $status = ai_auto_blog_get_job_status($result['job_id']);
            echo "Job Status: " . $status['status'] . "<br>\n";
            echo "Message: " . $status['message'] . "<br>\n";
            
        } else {
            echo "❌ Failed to create async job: " . $result['message'] . "<br>\n";
        }
    } catch (Exception $e) {
        echo "❌ Exception during job creation: " . $e->getMessage() . "<br>\n";
    }
}

// Test 4: Check WordPress hooks
echo "<h3>Test 5: WordPress Hooks</h3>\n";
if (has_action('ai_auto_blog_poll_job')) {
    echo "✅ Polling hook is registered<br>\n";
} else {
    echo "❌ Polling hook is not registered<br>\n";
}

if (has_action('wp_ajax_ai_auto_blog_check_status')) {
    echo "✅ AJAX status check hook is registered<br>\n";
} else {
    echo "❌ AJAX status check hook is not registered<br>\n";
}

echo "<h3>Test Complete</h3>\n";
echo "Check the logs for detailed information about any issues.<br>\n";
?>
