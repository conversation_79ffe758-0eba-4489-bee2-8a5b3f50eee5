<?php
if (!defined('ABSPATH')) exit;

require_once plugin_dir_path(__FILE__) . 'logs.php';
require_once ABSPATH . 'wp-admin/includes/media.php';
require_once ABSPATH . 'wp-admin/includes/file.php';
require_once ABSPATH . 'wp-admin/includes/image.php';

class AI_Auto_Blog_Poster {
    private static $retry_attempts = 3;
    private static $retry_delay = 5;
    private static $ai_api_timeout = 300; // 5 minutes default timeout for AI API
    private static $polling_interval = 10; // 10 seconds between polls
    private static $max_polling_time = 600; // 10 minutes max polling time

    public static function fetch_and_post() {
        $lock_key = 'ai_auto_blog_lock';

        // تحقق مما إذا كانت الوظيفة تعمل بالفعل
        if (get_transient($lock_key)) {
            ai_auto_blog_log('WARNING: Process already running, skipping execution.');
            return [
                'status' => 'error',
                'message' => __('Process already running, skipping execution.', 'ai-auto-blog')
            ];
        }

        // تعيين القفل لمدة قصيرة (مثلاً 5 دقائق)
        set_transient($lock_key, true, 300);
        try {
            ai_auto_blog_log('INFO: Process started - ' . current_time('mysql'));

            $settings = self::validate_settings();
            $ai_content = self::fetch_ai_content($settings);
            $post_id = self::create_wordpress_post($settings, $ai_content);

            if (!empty($ai_content['image_url'])) {
                self::attach_featured_image($post_id, $ai_content['image_url']);
            }

            ai_auto_blog_log("SUCCESS: Post published - ID {$post_id}");
            return [
                'status' => 'success',
                'post_id' => $post_id,
                'message' => __('Post published successfully', 'ai-auto-blog')
            ];

        } catch (Exception $e) {
            ai_auto_blog_log("ERROR: " . $e->getMessage());
            return [
                'status' => 'error',
                'message' => $e->getMessage()
            ];
        }
        finally {
            // إزالة القفل بعد انتهاء العملية
            delete_transient($lock_key);
        }
    }

    /**
     * Start asynchronous blog post generation
     * Returns immediately with job ID for status tracking
     */
    public static function start_async_post_generation() {
        try {
            ai_auto_blog_log('INFO: Starting async post generation - ' . current_time('mysql'));

            $settings = self::validate_settings();

            // Create job record
            $job_id = self::create_job_record($settings);

            // Start initial AI request
            $session_data = self::initiate_ai_request($settings, $job_id);

            // The initiate_ai_request now schedules background processing
            ai_auto_blog_log("SUCCESS: Async job started - Job ID {$job_id}, background processing scheduled");
            return [
                'status' => 'success',
                'job_id' => $job_id,
                'message' => __('Post generation started! AI request is being processed in background...', 'ai-auto-blog')
            ];

        } catch (Exception $e) {
            ai_auto_blog_log("ERROR: " . $e->getMessage());
            return [
                'status' => 'error',
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Create a job record in the database for tracking async operations
     */
    private static function create_job_record($settings) {
        global $wpdb;

        $job_id = wp_generate_uuid4();
        $table_name = $wpdb->prefix . 'ai_blog_jobs';

        // Create table if it doesn't exist
        self::create_jobs_table();

        $wpdb->insert(
            $table_name,
            [
                'job_id' => $job_id,
                'status' => 'initiated',
                'settings' => json_encode($settings),
                'created_at' => current_time('mysql'),
                'updated_at' => current_time('mysql')
            ],
            ['%s', '%s', '%s', '%s', '%s']
        );

        if ($wpdb->last_error) {
            throw new Exception('Failed to create job record: ' . $wpdb->last_error);
        }

        return $job_id;
    }

    /**
     * Create the jobs table if it doesn't exist
     */
    private static function create_jobs_table() {
        global $wpdb;

        $table_name = $wpdb->prefix . 'ai_blog_jobs';

        $charset_collate = $wpdb->get_charset_collate();

        $sql = "CREATE TABLE IF NOT EXISTS $table_name (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            job_id varchar(36) NOT NULL,
            status varchar(50) NOT NULL,
            settings longtext,
            session_data longtext,
            result_data longtext,
            error_message text,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY job_id (job_id),
            KEY status (status)
        ) $charset_collate;";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }

    /**
     * Update job status and data
     */
    private static function update_job_status($job_id, $status, $data = []) {
        global $wpdb;

        $table_name = $wpdb->prefix . 'ai_blog_jobs';

        $update_data = [
            'status' => $status,
            'updated_at' => current_time('mysql')
        ];

        if (!empty($data)) {
            if (isset($data['session_id']) || isset($data['chat_id']) || isset($data['request_data'])) {
                $update_data['session_data'] = json_encode($data);
            }
            if (isset($data['result'])) {
                $update_data['result_data'] = json_encode($data['result']);
            }
            if (isset($data['error'])) {
                $update_data['error_message'] = $data['error'];
            }
        }

        ai_auto_blog_log("INFO: Updating job {$job_id} to status {$status} with data: " . json_encode($data));

        $result = $wpdb->update(
            $table_name,
            $update_data,
            ['job_id' => $job_id],
            array_fill(0, count($update_data), '%s'),
            ['%s']
        );

        if ($result === false) {
            ai_auto_blog_log("ERROR: Failed to update job {$job_id}: " . $wpdb->last_error);
        } else {
            ai_auto_blog_log("INFO: Successfully updated job {$job_id}, rows affected: {$result}");
        }
    }

    private static function validate_settings() {
        $required = [
            'site_url' => get_option('ai_auto_blog_site_url'),
            'username' => get_option('ai_auto_blog_username'),
            'password' => get_option('ai_auto_blog_password'),
            'category' => get_option('ai_auto_blog_category'),
            'agent_url' => get_option('ai_auto_blog_agent_url'),
            'prompt' => get_option('ai_auto_blog_prompt')
        ];

        $missing = array_filter($required, function($v) { return empty($v); });
        if (!empty($missing)) {
            throw new Exception(__('Missing required settings: ', 'ai-auto-blog') . implode(', ', array_keys($missing)));
        }

        // Ensure category is a valid integer
        $required['category'] = intval($required['category']);
        if ($required['category'] <= 0 || !term_exists($required['category'], 'category')) {
            throw new Exception(__('Invalid category ID', 'ai-auto-blog'));
        }

        return $required;
    }

    /**
     * Initiate AI request using background processing to avoid timeouts
     * This creates a job and schedules the actual AI request in background
     */
    private static function initiate_ai_request($settings, $job_id) {
        // Use the EXACT prompt from settings - don't modify it
        $prompt = $settings['prompt'];

        ai_auto_blog_log("INFO: Scheduling background AI request for job {$job_id} with original prompt");

        // Store the request data for background processing
        $request_data = [
            'prompt' => $prompt,
            'site_url' => $settings['site_url'],
            'username' => $settings['username'],
            'password' => $settings['password'],
            'agent_url' => $settings['agent_url']
        ];

        ai_auto_blog_log("INFO: Storing request data for job {$job_id}: " . json_encode($request_data));

        self::update_job_status($job_id, 'ai_initiating', [
            'request_data' => $request_data
        ]);

        // Schedule immediate background request
        wp_schedule_single_event(time() + 1, 'ai_auto_blog_send_ai_request', [$job_id]);

        // Generate a temporary session ID for tracking
        $temp_session_id = wp_generate_uuid4();

        return [
            'session_id' => $temp_session_id,
            'chat_id' => $temp_session_id,
            'immediate_result' => false
        ];
    }

    /**
     * Send the actual AI request in background to avoid frontend timeouts
     */
    public static function send_ai_request_background($job_id) {
        global $wpdb;

        $table_name = $wpdb->prefix . 'ai_blog_jobs';

        // Get job data
        $job = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $table_name WHERE job_id = %s",
            $job_id
        ));

        if (!$job) {
            ai_auto_blog_log("ERROR: Job {$job_id} not found for AI request");
            return;
        }

        if ($job->status !== 'ai_initiating') {
            ai_auto_blog_log("INFO: Job {$job_id} status is {$job->status}, skipping AI request");
            return;
        }

        // Debug: Log what we have
        ai_auto_blog_log("INFO: Processing background AI request for job {$job_id}");
        ai_auto_blog_log("INFO: Job status: {$job->status}");
        ai_auto_blog_log("INFO: Session data: " . ($job->session_data ?? 'NULL'));
        ai_auto_blog_log("INFO: Settings data: " . ($job->settings ?? 'NULL'));

        // Try to get request data from session_data first, then from settings
        $session_data = json_decode($job->session_data, true);
        $request_data = $session_data['request_data'] ?? [];

        ai_auto_blog_log("INFO: Parsed session data: " . json_encode($session_data));
        ai_auto_blog_log("INFO: Extracted request data: " . json_encode($request_data));

        // If no request data in session, get from original settings
        if (empty($request_data)) {
            $settings = json_decode($job->settings, true);
            if (!empty($settings)) {
                $request_data = [
                    'prompt' => $settings['prompt'],
                    'site_url' => $settings['site_url'],
                    'username' => $settings['username'],
                    'password' => $settings['password'],
                    'agent_url' => $settings['agent_url']
                ];
                ai_auto_blog_log("INFO: Using settings data for job {$job_id}: " . json_encode($request_data));
            }
        }

        if (empty($request_data) || empty($request_data['prompt'])) {
            $error_msg = 'Missing request data - prompt: ' . (isset($request_data['prompt']) ? 'exists' : 'missing');
            self::update_job_status($job_id, 'failed', ['error' => $error_msg]);
            ai_auto_blog_log("ERROR: {$error_msg} for job {$job_id}");
            return;
        }

        try {
            ai_auto_blog_log("INFO: Sending background AI request for job {$job_id}");

            // Prepare the payload
            $payload = [
                'question' => "Prompt: {$request_data['prompt']}\n"
                            . "Site URL: {$request_data['site_url']}\n"
                            . "Username: {$request_data['username']}\n"
                            . "Password: {$request_data['password']}"
            ];

            $agent_url = $request_data['agent_url'];
            if (strpos($agent_url, 'https://') !== 0) {
                $agent_url = "https://llminabox.criticalfutureglobal.com/api/v1/prediction/{$agent_url}";
            }

            ai_auto_blog_log("INFO: Sending background request to: {$agent_url}");

            // Send the request with longer timeout since we're in background
            $response = wp_remote_post($agent_url, [
                'headers' => [
                    'Content-Type' => 'application/json',
                    'Accept' => 'application/json',
                    'User-Agent' => 'BloggBoss/1.0 WordPress/' . get_bloginfo('version')
                ],
                'body' => json_encode($payload),
                'timeout' => 120, // Longer timeout in background
                'sslverify' => false,
                'blocking' => true,
                'httpversion' => '1.1'
            ]);

            if (is_wp_error($response)) {
                $error_message = $response->get_error_message();

                // Check if it's a timeout error - this is expected for long AI processing
                if (strpos($error_message, 'timeout') !== false || strpos($error_message, 'timed out') !== false) {
                    ai_auto_blog_log("INFO: Request timed out for job {$job_id} - this is expected. AI is likely still processing. Starting polling...");

                    // Generate a session ID for polling and start polling
                    $temp_session_id = wp_generate_uuid4();
                    self::update_job_status($job_id, 'ai_processing', [
                        'session_id' => $temp_session_id,
                        'chat_id' => $temp_session_id,
                        'agent_url' => $request_data['agent_url']
                    ]);

                    // Schedule polling
                    wp_schedule_single_event(time() + self::$polling_interval, 'ai_auto_blog_poll_job', [$job_id]);
                    return;
                } else {
                    throw new Exception('Background AI request failed: ' . $error_message);
                }
            }

            $http_code = wp_remote_retrieve_response_code($response);
            $body = wp_remote_retrieve_body($response);

            ai_auto_blog_log("INFO: Background AI response for job {$job_id} - HTTP Code: {$http_code}, Body: " . substr($body, 0, 500) . "...");

            // Handle 504 Gateway Timeout - AI is processing but nginx timed out
            if ($http_code === 504) {
                ai_auto_blog_log("INFO: Got 504 timeout for job {$job_id} - AI is processing, starting polling...");

                // Generate a session ID for polling and start polling
                $temp_session_id = wp_generate_uuid4();
                self::update_job_status($job_id, 'ai_processing', [
                    'session_id' => $temp_session_id,
                    'chat_id' => $temp_session_id,
                    'agent_url' => $request_data['agent_url']
                ]);

                // Schedule polling
                wp_schedule_single_event(time() + self::$polling_interval, 'ai_auto_blog_poll_job', [$job_id]);
                return;
            }

            if ($http_code !== 200) {
                throw new Exception("AI API returned status code: {$http_code}, Body: " . $body);
            }

            $decoded_body = json_decode($body, true);

            // Check if we got a complete response
            if (isset($decoded_body['text']) && !empty($decoded_body['text'])) {
                $text = trim($decoded_body['text']);

                // If we got substantial content, store it and mark as complete
                if (strlen($text) > 100 && str_word_count($text) > 50) {
                    ai_auto_blog_log("INFO: Got complete response in background for job {$job_id}");

                    // Store the complete result
                    self::update_job_status($job_id, 'ai_complete', [
                        'result' => [
                            'content' => $decoded_body['text'],
                            'image_url' => $decoded_body['image_url'] ?? ''
                        ],
                        'session_id' => $decoded_body['sessionId'] ?? $decoded_body['chatId'] ?? wp_generate_uuid4(),
                        'chat_id' => $decoded_body['chatId'] ?? $decoded_body['sessionId'] ?? wp_generate_uuid4()
                    ]);

                    // Schedule immediate post creation
                    wp_schedule_single_event(time() + 1, 'ai_auto_blog_process_complete_job', [$job_id]);
                    return;
                }
            }

            // If no immediate result, set up for polling
            if (isset($decoded_body['sessionId']) || isset($decoded_body['chatId'])) {
                self::update_job_status($job_id, 'ai_processing', [
                    'session_id' => $decoded_body['sessionId'] ?? $decoded_body['chatId'],
                    'chat_id' => $decoded_body['chatId'] ?? $decoded_body['sessionId']
                ]);

                // Schedule polling
                wp_schedule_single_event(time() + self::$polling_interval, 'ai_auto_blog_poll_job', [$job_id]);
                ai_auto_blog_log("INFO: Background request initiated, polling scheduled for job {$job_id}");
            } else {
                throw new Exception('No session ID or chat ID in AI response: ' . json_encode($decoded_body));
            }

        } catch (Exception $e) {
            self::update_job_status($job_id, 'failed', ['error' => $e->getMessage()]);
            ai_auto_blog_log("ERROR: Background AI request failed for job {$job_id} - " . $e->getMessage());
        }
    }

    /**
     * Schedule background polling for a job
     */
    private static function schedule_polling($job_id) {
        // Schedule first poll in 10 seconds
        wp_schedule_single_event(time() + self::$polling_interval, 'ai_auto_blog_poll_job', [$job_id]);
        ai_auto_blog_log("INFO: Scheduled polling for job {$job_id}");
    }

    /**
     * Poll for job completion using session ID
     */
    public static function poll_job_status($job_id) {
        global $wpdb;

        $table_name = $wpdb->prefix . 'ai_blog_jobs';

        // Get job data
        $job = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $table_name WHERE job_id = %s",
            $job_id
        ));

        if (!$job) {
            ai_auto_blog_log("ERROR: Job {$job_id} not found");
            return;
        }

        if ($job->status !== 'ai_processing') {
            ai_auto_blog_log("INFO: Job {$job_id} status is {$job->status}, skipping poll");
            return;
        }

        $session_data = json_decode($job->session_data, true);
        $settings = json_decode($job->settings, true);

        if (!$session_data || !isset($session_data['session_id'])) {
            self::update_job_status($job_id, 'failed', ['error' => 'Missing session data']);
            return;
        }

        try {
            // Check if AI processing is complete by checking chat history
            $ai_content = self::check_ai_completion($settings, $session_data);

            if ($ai_content) {
                // AI is complete, store result and schedule post creation
                ai_auto_blog_log("INFO: AI processing complete for job {$job_id}, scheduling post creation");

                self::update_job_status($job_id, 'ai_complete', [
                    'result' => $ai_content
                ]);

                // Schedule immediate post creation
                wp_schedule_single_event(time() + 1, 'ai_auto_blog_process_complete_job', [$job_id]);

            } else {
                // Still processing, check if we should continue polling
                $created_time = strtotime($job->created_at);
                $elapsed_time = time() - $created_time;

                if ($elapsed_time > self::$max_polling_time) {
                    self::update_job_status($job_id, 'timeout', ['error' => 'AI processing timeout after ' . $elapsed_time . ' seconds']);
                    ai_auto_blog_log("ERROR: Job {$job_id} timed out after {$elapsed_time} seconds");
                } else {
                    // Schedule next poll
                    wp_schedule_single_event(time() + self::$polling_interval, 'ai_auto_blog_poll_job', [$job_id]);
                    ai_auto_blog_log("INFO: Job {$job_id} still processing, scheduled next poll in " . self::$polling_interval . " seconds");
                }
            }

        } catch (Exception $e) {
            self::update_job_status($job_id, 'failed', ['error' => $e->getMessage()]);
            ai_auto_blog_log("ERROR: Job {$job_id} failed - " . $e->getMessage());
        }
    }

    /**
     * Process a job that has completed AI generation
     */
    public static function process_complete_job($job_id) {
        global $wpdb;

        $table_name = $wpdb->prefix . 'ai_blog_jobs';

        // Get job data
        $job = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $table_name WHERE job_id = %s",
            $job_id
        ));

        if (!$job) {
            ai_auto_blog_log("ERROR: Job {$job_id} not found for processing");
            return;
        }

        if ($job->status !== 'ai_complete') {
            ai_auto_blog_log("INFO: Job {$job_id} status is {$job->status}, not ready for processing");
            return;
        }

        $settings = json_decode($job->settings, true);
        $result_data = json_decode($job->result_data, true);

        if (!$result_data || !isset($result_data['content'])) {
            self::update_job_status($job_id, 'failed', ['error' => 'Missing AI content data']);
            return;
        }

        try {
            ai_auto_blog_log("INFO: Creating WordPress post for job {$job_id}");
            self::update_job_status($job_id, 'creating_post');

            $ai_content = $result_data;
            $post_id = self::create_wordpress_post($settings, $ai_content);

            if (!empty($ai_content['image_url'])) {
                self::attach_featured_image($post_id, $ai_content['image_url']);
            }

            self::update_job_status($job_id, 'completed', [
                'result' => [
                    'post_id' => $post_id,
                    'post_url' => get_permalink($post_id)
                ]
            ]);

            ai_auto_blog_log("SUCCESS: Job {$job_id} completed - Post ID {$post_id}");

        } catch (Exception $e) {
            self::update_job_status($job_id, 'failed', ['error' => $e->getMessage()]);
            ai_auto_blog_log("ERROR: Job {$job_id} failed during post creation - " . $e->getMessage());
        }
    }

    private static function fetch_ai_content($settings) {
        $fast_mode = get_option('ai_auto_blog_fast_mode', 1);

        if ($fast_mode) {
            // Fast mode: shorter prompt and timeout for nginx compatibility
            $prompt = "Write a brief 300-word blog post about: " . substr($settings['prompt'], 0, 100);
            $timeout = 50; // Stay well under nginx 60s limit
            ai_auto_blog_log("INFO: Fast mode enabled - using 50s timeout and shorter prompt");
        } else {
            // Full mode: original prompt and timeout
            $prompt = $settings['prompt'];
            $timeout = get_option('ai_auto_blog_api_timeout', self::$ai_api_timeout);
            ai_auto_blog_log("INFO: Full mode enabled - using {$timeout}s timeout and full prompt");
        }

        $payload = [
            'question' => "Prompt: {$prompt}\n"
                        . "Site URL: {$settings['site_url']}\n"
                        . "Username: {$settings['username']}\n"
                        . "Password: {$settings['password']}"
                        . ($fast_mode ? "\nIMPORTANT: Keep response under 300 words for faster processing." : "")
        ];

        $agent_url = $settings['agent_url'];
        if (strpos($agent_url, 'https://') !== 0) {
            $agent_url = "https://llminabox.criticalfutureglobal.com/api/v1/prediction/{$agent_url}";
        }

        ai_auto_blog_log("INFO: Using AI API timeout: {$timeout} seconds");

        for ($attempt = 1; $attempt <= self::$retry_attempts; $attempt++) {
            ai_auto_blog_log("INFO: Attempt {$attempt} - Sending request to: {$agent_url}");
            ai_auto_blog_log("INFO: Payload: " . json_encode($payload));
            ai_auto_blog_log("INFO: Timeout: {$timeout} seconds");

            $response = wp_remote_post($agent_url, [
                'headers' => [
                    'Content-Type' => 'application/json',
                    'Accept' => 'application/json',
                    'User-Agent' => 'BloggBoss/1.0 WordPress/' . get_bloginfo('version')
                ],
                'body' => json_encode($payload),
                'timeout' => $timeout, // Configurable timeout for AI processing
                'sslverify' => false, // Temporarily disable SSL verification for testing
                'blocking' => true,
                'httpversion' => '1.1'
            ]);

            ai_auto_blog_log("INFO: Request completed, processing response...");

            if (is_wp_error($response)) {
                $error_message = $response->get_error_message();
                ai_auto_blog_log("ERROR: WP_Error in API request - " . $error_message);

                // Check if it's a timeout error
                if (strpos($error_message, 'timeout') !== false || strpos($error_message, 'timed out') !== false) {
                    ai_auto_blog_log("WARNING: Request timed out after {$timeout} seconds. Consider increasing the timeout setting or nginx proxy timeout.");
                }

                if ($attempt < self::$retry_attempts) {
                    sleep(self::$retry_delay);
                    ai_auto_blog_log("WARNING: AI API attempt {$attempt} failed, retrying...");
                    continue;
                }
            } else {
                $http_code = wp_remote_retrieve_response_code($response);
                $body = wp_remote_retrieve_body($response);

                ai_auto_blog_log("INFO: API Response - HTTP Code: {$http_code}, Body: " . substr($body, 0, 500) . "...");

                if ($http_code === 200) {
                    $decoded_body = json_decode($body, true);
                    if (isset($decoded_body['text'])) {
                        return [
                            'content' => $decoded_body['text'],
                            'image_url' => $decoded_body['image_url'] ?? ''
                        ];
                    } else {
                        ai_auto_blog_log("ERROR: API response doesn't contain 'text' field");
                    }
                } else if ($http_code === 504) {
                    ai_auto_blog_log("ERROR: 504 Gateway Timeout - This is a server infrastructure issue. The nginx server timed out waiting for Flowise response.");
                    ai_auto_blog_log("SOLUTION: Increase nginx proxy_read_timeout, proxy_connect_timeout, and proxy_send_timeout to 600s or more.");
                } else {
                    ai_auto_blog_log("ERROR: API returned non-200 status code: {$http_code}");
                }
            }

            if ($attempt < self::$retry_attempts) {
                sleep(self::$retry_delay);
                ai_auto_blog_log("WARNING: AI API attempt {$attempt} failed, retrying...");
            }
        }

        throw new Exception(__('Failed to fetch AI content after multiple attempts', 'ai-auto-blog'));
    }

    /**
     * Check if AI processing is complete by trying the original request again
     * If the AI has finished processing, it should return the result quickly
     */
    private static function check_ai_completion($settings, $session_data) {
        global $wpdb;

        // Get the original request data
        $table_name = $wpdb->prefix . 'ai_blog_jobs';
        $job = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $table_name WHERE session_data LIKE %s",
            '%' . $session_data['session_id'] . '%'
        ));

        if (!$job) {
            ai_auto_blog_log("WARNING: Could not find job for session " . $session_data['session_id']);
            return false;
        }

        // Get the original request data
        $job_session_data = json_decode($job->session_data, true);
        $original_settings = json_decode($job->settings, true);

        if (!$original_settings) {
            ai_auto_blog_log("WARNING: Could not get original settings for completion check");
            return false;
        }

        $agent_url = $original_settings['agent_url'];
        if (strpos($agent_url, 'https://') !== 0) {
            $agent_url = "https://llminabox.criticalfutureglobal.com/api/v1/prediction/{$agent_url}";
        }

        ai_auto_blog_log("INFO: Checking AI completion by retrying original request");

        // Try the original request again with a short timeout
        $payload = [
            'question' => "Prompt: {$original_settings['prompt']}\n"
                        . "Site URL: {$original_settings['site_url']}\n"
                        . "Username: {$original_settings['username']}\n"
                        . "Password: {$original_settings['password']}"
        ];

        $response = wp_remote_post($agent_url, [
            'headers' => [
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
                'User-Agent' => 'BloggBoss/1.0 WordPress/' . get_bloginfo('version')
            ],
            'body' => json_encode($payload),
            'timeout' => 45, // Shorter timeout for polling
            'sslverify' => false,
            'blocking' => true,
            'httpversion' => '1.1'
        ]);

        if (is_wp_error($response)) {
            $error_message = $response->get_error_message();
            if (strpos($error_message, 'timeout') !== false) {
                ai_auto_blog_log("INFO: Still timing out, AI likely still processing");
                return false;
            }
            ai_auto_blog_log("WARNING: Failed to check AI completion - " . $error_message);
            return false;
        }

        $http_code = wp_remote_retrieve_response_code($response);
        $body = wp_remote_retrieve_body($response);

        ai_auto_blog_log("INFO: Completion check response - HTTP Code: {$http_code}, Body: " . substr($body, 0, 300) . "...");

        if ($http_code === 200) {
            $decoded_body = json_decode($body, true);

            if (isset($decoded_body['text']) && !empty($decoded_body['text'])) {
                $text = trim($decoded_body['text']);

                // Check if this looks like a substantial blog post
                if (strlen($text) > 300 && str_word_count($text) > 150) {
                    ai_auto_blog_log("INFO: Found complete blog content (" . strlen($text) . " chars, " . str_word_count($text) . " words)");
                    return [
                        'content' => $text,
                        'image_url' => $decoded_body['image_url'] ?? ''
                    ];
                } else {
                    ai_auto_blog_log("INFO: Response too short (" . strlen($text) . " chars), AI likely still processing");
                    return false;
                }
            }
        } else if ($http_code === 504 || $http_code === 408) {
            // Still processing
            ai_auto_blog_log("INFO: AI still processing (timeout response)");
            return false;
        } else {
            ai_auto_blog_log("WARNING: Unexpected status code when checking AI completion: {$http_code}");
        }

        return false;
    }

    /**
     * Get job status for frontend
     */
    public static function get_job_status($job_id) {
        global $wpdb;

        $table_name = $wpdb->prefix . 'ai_blog_jobs';

        $job = $wpdb->get_row($wpdb->prepare(
            "SELECT job_id, status, result_data, error_message, created_at, updated_at FROM $table_name WHERE job_id = %s",
            $job_id
        ));

        if (!$job) {
            return [
                'status' => 'not_found',
                'message' => __('Job not found', 'ai-auto-blog')
            ];
        }

        $result = [
            'status' => $job->status,
            'created_at' => $job->created_at,
            'updated_at' => $job->updated_at
        ];

        switch ($job->status) {
            case 'initiated':
                $result['message'] = __('Job initiated', 'ai-auto-blog');
                break;
            case 'ai_initiating':
                $result['message'] = __('Sending request to AI agent...', 'ai-auto-blog');
                break;
            case 'ai_processing':
                $result['message'] = __('AI is generating content...', 'ai-auto-blog');
                break;
            case 'ai_complete':
                $result['message'] = __('AI content ready, creating WordPress post...', 'ai-auto-blog');
                break;
            case 'creating_post':
                $result['message'] = __('Creating WordPress post...', 'ai-auto-blog');
                break;
            case 'completed':
                $result['message'] = __('Post created successfully!', 'ai-auto-blog');
                if ($job->result_data) {
                    $result['result'] = json_decode($job->result_data, true);
                }
                break;
            case 'failed':
                $result['message'] = __('Job failed: ', 'ai-auto-blog') . $job->error_message;
                break;
            case 'timeout':
                $result['message'] = __('Job timed out', 'ai-auto-blog');
                break;
            default:
                $result['message'] = __('Unknown status', 'ai-auto-blog');
        }

        return $result;
    }

    private static function create_wordpress_post($settings, $content) {
        preg_match('/<h1[^>]*>(.*?)<\/h1>/i', $content['content'], $matches);
        $title = $matches[1] ?? __('$title', 'ai-auto-blog');
        
        $cleaned_content = wp_kses_post($content['content']);
        
        ai_auto_blog_log("INFO: Preparing post data - Title: " . $title);
        
        $post_data = [
            'title' => sanitize_text_field($title),
            'content' => $cleaned_content,
            'status' => 'publish',
            'categories' => [intval($settings['category'])],
            'meta' => [
                'ai_generated' => true,
                'ai_prompt' => $settings['prompt']
            ]
        ];

        $api_url = trailingslashit($settings['site_url']) . 'wp-json/wp/v2/posts';
        ai_auto_blog_log("INFO: WordPress API URL: " . $api_url);

        $auth = base64_encode($settings['username'] . ':' . $settings['password']);
        
        $response = wp_remote_post($api_url, [
            'headers' => [
                'Authorization' => "Basic " . $auth,
                'Content-Type' => 'application/json',
                'Accept' => 'application/json'
            ],
            'body' => json_encode($post_data),
            'timeout' => 60,
            'sslverify' => true,
            'data_format' => 'body'
        ]);

        if (is_wp_error($response)) {
            $error_message = $response->get_error_message();
            ai_auto_blog_log("ERROR: WordPress API error - " . $error_message);
            throw new Exception(__('WordPress API error: ', 'ai-auto-blog') . $error_message);
        }

        $response_code = wp_remote_retrieve_response_code($response);
        $response_body = wp_remote_retrieve_body($response);
        
        ai_auto_blog_log("INFO: WordPress API Response Code: " . $response_code);
        ai_auto_blog_log("INFO: WordPress API Response Body: " . substr($response_body, 0, 500));

        if ($response_code !== 201) {
            $error_message = sprintf(
                __('Failed to create post. Status code: %d, Response: %s', 'ai-auto-blog'),
                $response_code,
                $response_body
            );
            ai_auto_blog_log("ERROR: " . $error_message);
            throw new Exception($error_message);
        }

        $response_data = json_decode($response_body, true);
        
        if (!isset($response_data['id'])) {
            ai_auto_blog_log("ERROR: No post ID in response - " . print_r($response_data, true));
            throw new Exception(__('Failed to get post ID from response', 'ai-auto-blog'));
        }

        ai_auto_blog_log("SUCCESS: Post created with ID: " . $response_data['id']);
        return $response_data['id'];
    }

    private static function attach_featured_image($post_id, $image_url) {
        try {
            if (empty($image_url)) {
                ai_auto_blog_log("INFO: No image URL provided for featured image");
                return;
            }

            ai_auto_blog_log("INFO: Attempting to attach featured image from URL: " . $image_url);
            
            $image_id = media_sideload_image($image_url, $post_id, __('AI Generated Featured Image', 'ai-auto-blog'), 'id');
            
            if (is_wp_error($image_id)) {
                throw new Exception($image_id->get_error_message());
            }

            $result = set_post_thumbnail($post_id, $image_id);
            
            if ($result) {
                ai_auto_blog_log("SUCCESS: Featured image attached - ID {$image_id}");
            } else {
                throw new Exception("Failed to set post thumbnail");
            }

        } catch (Exception $e) {
            ai_auto_blog_log("WARNING: Failed to attach featured image - " . $e->getMessage());
        }
    }

    /**
     * Public method to create jobs table (for plugin activation)
     */
    public static function create_jobs_table_public() {
        self::create_jobs_table();
    }
}

function ai_auto_blog_fetch_and_post() {
    return AI_Auto_Blog_Poster::fetch_and_post();
}

function ai_auto_blog_start_async_generation() {
    return AI_Auto_Blog_Poster::start_async_post_generation();
}

function ai_auto_blog_get_job_status($job_id) {
    return AI_Auto_Blog_Poster::get_job_status($job_id);
}

// Hook for background polling
add_action('ai_auto_blog_poll_job', [AI_Auto_Blog_Poster::class, 'poll_job_status']);

// Hook for processing completed jobs
add_action('ai_auto_blog_process_complete_job', [AI_Auto_Blog_Poster::class, 'process_complete_job']);

// Hook for background AI request sending
add_action('ai_auto_blog_send_ai_request', [AI_Auto_Blog_Poster::class, 'send_ai_request_background']);

