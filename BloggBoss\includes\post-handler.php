<?php
if (!defined('ABSPATH')) exit;

require_once plugin_dir_path(__FILE__) . 'logs.php';
require_once ABSPATH . 'wp-admin/includes/media.php';
require_once ABSPATH . 'wp-admin/includes/file.php';
require_once ABSPATH . 'wp-admin/includes/image.php';

class AI_Auto_Blog_Poster {
    private static $retry_attempts = 3;
    private static $retry_delay = 5;
    private static $ai_api_timeout = 300; // 5 minutes default timeout for AI API
    private static $polling_interval = 10; // 10 seconds between polls
    private static $max_polling_time = 600; // 10 minutes max polling time

    public static function fetch_and_post() {
        $lock_key = 'ai_auto_blog_lock';

        // تحقق مما إذا كانت الوظيفة تعمل بالفعل
        if (get_transient($lock_key)) {
            ai_auto_blog_log('WARNING: Process already running, skipping execution.');
            return [
                'status' => 'error',
                'message' => __('Process already running, skipping execution.', 'ai-auto-blog')
            ];
        }

        // تعيين القفل لمدة قصيرة (مثلاً 5 دقائق)
        set_transient($lock_key, true, 300);
        try {
            ai_auto_blog_log('INFO: Process started - ' . current_time('mysql'));

            $settings = self::validate_settings();
            $ai_content = self::fetch_ai_content($settings);
            $post_id = self::create_wordpress_post($settings, $ai_content);

            if (!empty($ai_content['image_url'])) {
                self::attach_featured_image($post_id, $ai_content['image_url']);
            }

            ai_auto_blog_log("SUCCESS: Post published - ID {$post_id}");
            return [
                'status' => 'success',
                'post_id' => $post_id,
                'message' => __('Post published successfully', 'ai-auto-blog')
            ];

        } catch (Exception $e) {
            ai_auto_blog_log("ERROR: " . $e->getMessage());
            return [
                'status' => 'error',
                'message' => $e->getMessage()
            ];
        }
        finally {
            // إزالة القفل بعد انتهاء العملية
            delete_transient($lock_key);
        }
    }

    /**
     * Start asynchronous blog post generation
     * Returns immediately with job ID for status tracking
     */
    public static function start_async_post_generation() {
        try {
            ai_auto_blog_log('INFO: Starting async post generation - ' . current_time('mysql'));

            $settings = self::validate_settings();

            // Create job record
            $job_id = self::create_job_record($settings);

            // Start initial AI request (non-blocking)
            $session_data = self::initiate_ai_request($settings, $job_id);

            // Update job with session info
            self::update_job_status($job_id, 'ai_processing', [
                'session_id' => $session_data['session_id'],
                'chat_id' => $session_data['chat_id']
            ]);

            // Schedule background polling
            self::schedule_polling($job_id);

            ai_auto_blog_log("SUCCESS: Async job started - Job ID {$job_id}");
            return [
                'status' => 'success',
                'job_id' => $job_id,
                'message' => __('Post generation started. You will be notified when complete.', 'ai-auto-blog')
            ];

        } catch (Exception $e) {
            ai_auto_blog_log("ERROR: " . $e->getMessage());
            return [
                'status' => 'error',
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Create a job record in the database for tracking async operations
     */
    private static function create_job_record($settings) {
        global $wpdb;

        $job_id = wp_generate_uuid4();
        $table_name = $wpdb->prefix . 'ai_blog_jobs';

        // Create table if it doesn't exist
        self::create_jobs_table();

        $wpdb->insert(
            $table_name,
            [
                'job_id' => $job_id,
                'status' => 'initiated',
                'settings' => json_encode($settings),
                'created_at' => current_time('mysql'),
                'updated_at' => current_time('mysql')
            ],
            ['%s', '%s', '%s', '%s', '%s']
        );

        if ($wpdb->last_error) {
            throw new Exception('Failed to create job record: ' . $wpdb->last_error);
        }

        return $job_id;
    }

    /**
     * Create the jobs table if it doesn't exist
     */
    private static function create_jobs_table() {
        global $wpdb;

        $table_name = $wpdb->prefix . 'ai_blog_jobs';

        $charset_collate = $wpdb->get_charset_collate();

        $sql = "CREATE TABLE IF NOT EXISTS $table_name (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            job_id varchar(36) NOT NULL,
            status varchar(50) NOT NULL,
            settings longtext,
            session_data longtext,
            result_data longtext,
            error_message text,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY job_id (job_id),
            KEY status (status)
        ) $charset_collate;";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }

    /**
     * Update job status and data
     */
    private static function update_job_status($job_id, $status, $data = []) {
        global $wpdb;

        $table_name = $wpdb->prefix . 'ai_blog_jobs';

        $update_data = [
            'status' => $status,
            'updated_at' => current_time('mysql')
        ];

        if (!empty($data)) {
            if (isset($data['session_id']) || isset($data['chat_id'])) {
                $update_data['session_data'] = json_encode($data);
            }
            if (isset($data['result'])) {
                $update_data['result_data'] = json_encode($data['result']);
            }
            if (isset($data['error'])) {
                $update_data['error_message'] = $data['error'];
            }
        }

        $wpdb->update(
            $table_name,
            $update_data,
            ['job_id' => $job_id],
            array_fill(0, count($update_data), '%s'),
            ['%s']
        );
    }

    private static function validate_settings() {
        $required = [
            'site_url' => get_option('ai_auto_blog_site_url'),
            'username' => get_option('ai_auto_blog_username'),
            'password' => get_option('ai_auto_blog_password'),
            'category' => get_option('ai_auto_blog_category'),
            'agent_url' => get_option('ai_auto_blog_agent_url'),
            'prompt' => get_option('ai_auto_blog_prompt')
        ];

        $missing = array_filter($required, function($v) { return empty($v); });
        if (!empty($missing)) {
            throw new Exception(__('Missing required settings: ', 'ai-auto-blog') . implode(', ', array_keys($missing)));
        }

        // Ensure category is a valid integer
        $required['category'] = intval($required['category']);
        if ($required['category'] <= 0 || !term_exists($required['category'], 'category')) {
            throw new Exception(__('Invalid category ID', 'ai-auto-blog'));
        }

        return $required;
    }

    /**
     * Initiate AI request and return session information immediately
     */
    private static function initiate_ai_request($settings, $job_id) {
        $fast_mode = get_option('ai_auto_blog_fast_mode', 1);

        if ($fast_mode) {
            $prompt = "Write a brief 300-word blog post about: " . substr($settings['prompt'], 0, 100);
            ai_auto_blog_log("INFO: Fast mode enabled for job {$job_id}");
        } else {
            $prompt = $settings['prompt'];
            ai_auto_blog_log("INFO: Full mode enabled for job {$job_id}");
        }

        $payload = [
            'question' => "Prompt: {$prompt}\n"
                        . "Site URL: {$settings['site_url']}\n"
                        . "Username: {$settings['username']}\n"
                        . "Password: {$settings['password']}"
                        . ($fast_mode ? "\nIMPORTANT: Keep response under 300 words for faster processing." : "")
        ];

        $agent_url = $settings['agent_url'];
        if (strpos($agent_url, 'https://') !== 0) {
            $agent_url = "https://llminabox.criticalfutureglobal.com/api/v1/prediction/{$agent_url}";
        }

        ai_auto_blog_log("INFO: Initiating AI request for job {$job_id} to: {$agent_url}");

        // Use shorter timeout for initial request - we just want the session ID
        $response = wp_remote_post($agent_url, [
            'headers' => [
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
                'User-Agent' => 'BloggBoss/1.0 WordPress/' . get_bloginfo('version')
            ],
            'body' => json_encode($payload),
            'timeout' => 30, // Short timeout - just to get session ID
            'sslverify' => false,
            'blocking' => true,
            'httpversion' => '1.1'
        ]);

        if (is_wp_error($response)) {
            throw new Exception('Failed to initiate AI request: ' . $response->get_error_message());
        }

        $http_code = wp_remote_retrieve_response_code($response);
        $body = wp_remote_retrieve_body($response);

        ai_auto_blog_log("INFO: Initial AI response for job {$job_id} - HTTP Code: {$http_code}");

        if ($http_code !== 200) {
            throw new Exception("AI API returned status code: {$http_code}");
        }

        $decoded_body = json_decode($body, true);
        if (!isset($decoded_body['sessionId'])) {
            throw new Exception('No session ID in AI response');
        }

        return [
            'session_id' => $decoded_body['sessionId'],
            'chat_id' => $decoded_body['chatId'] ?? $decoded_body['sessionId']
        ];
    }

    /**
     * Schedule background polling for a job
     */
    private static function schedule_polling($job_id) {
        // Schedule first poll in 10 seconds
        wp_schedule_single_event(time() + self::$polling_interval, 'ai_auto_blog_poll_job', [$job_id]);
        ai_auto_blog_log("INFO: Scheduled polling for job {$job_id}");
    }

    /**
     * Poll for job completion using session ID
     */
    public static function poll_job_status($job_id) {
        global $wpdb;

        $table_name = $wpdb->prefix . 'ai_blog_jobs';

        // Get job data
        $job = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $table_name WHERE job_id = %s",
            $job_id
        ));

        if (!$job) {
            ai_auto_blog_log("ERROR: Job {$job_id} not found");
            return;
        }

        if ($job->status !== 'ai_processing') {
            ai_auto_blog_log("INFO: Job {$job_id} status is {$job->status}, skipping poll");
            return;
        }

        $session_data = json_decode($job->session_data, true);
        $settings = json_decode($job->settings, true);

        if (!$session_data || !isset($session_data['session_id'])) {
            self::update_job_status($job_id, 'failed', ['error' => 'Missing session data']);
            return;
        }

        try {
            // Check if AI processing is complete
            $ai_content = self::check_ai_completion($settings, $session_data);

            if ($ai_content) {
                // AI is complete, create WordPress post
                ai_auto_blog_log("INFO: AI processing complete for job {$job_id}, creating post");
                self::update_job_status($job_id, 'creating_post');

                $post_id = self::create_wordpress_post($settings, $ai_content);

                if (!empty($ai_content['image_url'])) {
                    self::attach_featured_image($post_id, $ai_content['image_url']);
                }

                self::update_job_status($job_id, 'completed', [
                    'result' => [
                        'post_id' => $post_id,
                        'post_url' => get_permalink($post_id)
                    ]
                ]);

                ai_auto_blog_log("SUCCESS: Job {$job_id} completed - Post ID {$post_id}");

            } else {
                // Still processing, check if we should continue polling
                $created_time = strtotime($job->created_at);
                $elapsed_time = time() - $created_time;

                if ($elapsed_time > self::$max_polling_time) {
                    self::update_job_status($job_id, 'timeout', ['error' => 'AI processing timeout']);
                    ai_auto_blog_log("ERROR: Job {$job_id} timed out after {$elapsed_time} seconds");
                } else {
                    // Schedule next poll
                    wp_schedule_single_event(time() + self::$polling_interval, 'ai_auto_blog_poll_job', [$job_id]);
                    ai_auto_blog_log("INFO: Job {$job_id} still processing, scheduled next poll");
                }
            }

        } catch (Exception $e) {
            self::update_job_status($job_id, 'failed', ['error' => $e->getMessage()]);
            ai_auto_blog_log("ERROR: Job {$job_id} failed - " . $e->getMessage());
        }
    }

    private static function fetch_ai_content($settings) {
        $fast_mode = get_option('ai_auto_blog_fast_mode', 1);

        if ($fast_mode) {
            // Fast mode: shorter prompt and timeout for nginx compatibility
            $prompt = "Write a brief 300-word blog post about: " . substr($settings['prompt'], 0, 100);
            $timeout = 50; // Stay well under nginx 60s limit
            ai_auto_blog_log("INFO: Fast mode enabled - using 50s timeout and shorter prompt");
        } else {
            // Full mode: original prompt and timeout
            $prompt = $settings['prompt'];
            $timeout = get_option('ai_auto_blog_api_timeout', self::$ai_api_timeout);
            ai_auto_blog_log("INFO: Full mode enabled - using {$timeout}s timeout and full prompt");
        }

        $payload = [
            'question' => "Prompt: {$prompt}\n"
                        . "Site URL: {$settings['site_url']}\n"
                        . "Username: {$settings['username']}\n"
                        . "Password: {$settings['password']}"
                        . ($fast_mode ? "\nIMPORTANT: Keep response under 300 words for faster processing." : "")
        ];

        $agent_url = $settings['agent_url'];
        if (strpos($agent_url, 'https://') !== 0) {
            $agent_url = "https://llminabox.criticalfutureglobal.com/api/v1/prediction/{$agent_url}";
        }

        ai_auto_blog_log("INFO: Using AI API timeout: {$timeout} seconds");

        for ($attempt = 1; $attempt <= self::$retry_attempts; $attempt++) {
            ai_auto_blog_log("INFO: Attempt {$attempt} - Sending request to: {$agent_url}");
            ai_auto_blog_log("INFO: Payload: " . json_encode($payload));
            ai_auto_blog_log("INFO: Timeout: {$timeout} seconds");

            $response = wp_remote_post($agent_url, [
                'headers' => [
                    'Content-Type' => 'application/json',
                    'Accept' => 'application/json',
                    'User-Agent' => 'BloggBoss/1.0 WordPress/' . get_bloginfo('version')
                ],
                'body' => json_encode($payload),
                'timeout' => $timeout, // Configurable timeout for AI processing
                'sslverify' => false, // Temporarily disable SSL verification for testing
                'blocking' => true,
                'httpversion' => '1.1'
            ]);

            ai_auto_blog_log("INFO: Request completed, processing response...");

            if (is_wp_error($response)) {
                $error_message = $response->get_error_message();
                ai_auto_blog_log("ERROR: WP_Error in API request - " . $error_message);

                // Check if it's a timeout error
                if (strpos($error_message, 'timeout') !== false || strpos($error_message, 'timed out') !== false) {
                    ai_auto_blog_log("WARNING: Request timed out after {$timeout} seconds. Consider increasing the timeout setting or nginx proxy timeout.");
                }

                if ($attempt < self::$retry_attempts) {
                    sleep(self::$retry_delay);
                    ai_auto_blog_log("WARNING: AI API attempt {$attempt} failed, retrying...");
                    continue;
                }
            } else {
                $http_code = wp_remote_retrieve_response_code($response);
                $body = wp_remote_retrieve_body($response);

                ai_auto_blog_log("INFO: API Response - HTTP Code: {$http_code}, Body: " . substr($body, 0, 500) . "...");

                if ($http_code === 200) {
                    $decoded_body = json_decode($body, true);
                    if (isset($decoded_body['text'])) {
                        return [
                            'content' => $decoded_body['text'],
                            'image_url' => $decoded_body['image_url'] ?? ''
                        ];
                    } else {
                        ai_auto_blog_log("ERROR: API response doesn't contain 'text' field");
                    }
                } else if ($http_code === 504) {
                    ai_auto_blog_log("ERROR: 504 Gateway Timeout - This is a server infrastructure issue. The nginx server timed out waiting for Flowise response.");
                    ai_auto_blog_log("SOLUTION: Increase nginx proxy_read_timeout, proxy_connect_timeout, and proxy_send_timeout to 600s or more.");
                } else {
                    ai_auto_blog_log("ERROR: API returned non-200 status code: {$http_code}");
                }
            }

            if ($attempt < self::$retry_attempts) {
                sleep(self::$retry_delay);
                ai_auto_blog_log("WARNING: AI API attempt {$attempt} failed, retrying...");
            }
        }

        throw new Exception(__('Failed to fetch AI content after multiple attempts', 'ai-auto-blog'));
    }

    /**
     * Check if AI processing is complete using session ID
     * For Flowise, we need to make another request with the session ID to get the result
     */
    private static function check_ai_completion($settings, $session_data) {
        $agent_url = $settings['agent_url'];
        if (strpos($agent_url, 'https://') !== 0) {
            $agent_url = "https://llminabox.criticalfutureglobal.com/api/v1/prediction/{$agent_url}";
        }

        ai_auto_blog_log("INFO: Checking AI completion status for session: " . $session_data['session_id']);

        // For Flowise, we can try to get the chat history or make a simple request with the session ID
        $payload = [
            'question' => 'status', // Simple status check
            'sessionId' => $session_data['session_id']
        ];

        $response = wp_remote_post($agent_url, [
            'headers' => [
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
                'User-Agent' => 'BloggBoss/1.0 WordPress/' . get_bloginfo('version')
            ],
            'body' => json_encode($payload),
            'timeout' => 30,
            'sslverify' => false,
            'blocking' => true,
            'httpversion' => '1.1'
        ]);

        if (is_wp_error($response)) {
            ai_auto_blog_log("WARNING: Failed to check AI status - " . $response->get_error_message());
            return false;
        }

        $http_code = wp_remote_retrieve_response_code($response);
        $body = wp_remote_retrieve_body($response);

        ai_auto_blog_log("INFO: Status check response - HTTP Code: {$http_code}, Body: " . substr($body, 0, 200) . "...");

        if ($http_code === 200) {
            $decoded_body = json_decode($body, true);

            // Check if the response contains completed content
            // Look for a substantial response that's not just a status message
            if (isset($decoded_body['text']) && !empty($decoded_body['text'])) {
                $text = trim($decoded_body['text']);

                // Check if this looks like a real blog post (not just a status response)
                if (strlen($text) > 100 && (
                    strpos(strtolower($text), '<h1') !== false ||
                    strpos(strtolower($text), '<h2') !== false ||
                    strpos(strtolower($text), 'blog') !== false ||
                    str_word_count($text) > 50
                )) {
                    ai_auto_blog_log("INFO: AI processing completed, content received (" . strlen($text) . " chars)");
                    return [
                        'content' => $decoded_body['text'],
                        'image_url' => $decoded_body['image_url'] ?? ''
                    ];
                } else {
                    ai_auto_blog_log("INFO: Received response but appears to be status message, not blog content");
                    return false;
                }
            }
        } else if ($http_code === 202) {
            // Still processing
            ai_auto_blog_log("INFO: AI still processing (HTTP 202)");
            return false;
        } else if ($http_code === 504 || $http_code === 408) {
            // Timeout - likely still processing
            ai_auto_blog_log("INFO: Timeout response, AI likely still processing");
            return false;
        } else {
            ai_auto_blog_log("WARNING: Unexpected status code when checking AI completion: {$http_code}");
        }

        return false;
    }

    /**
     * Get job status for frontend
     */
    public static function get_job_status($job_id) {
        global $wpdb;

        $table_name = $wpdb->prefix . 'ai_blog_jobs';

        $job = $wpdb->get_row($wpdb->prepare(
            "SELECT job_id, status, result_data, error_message, created_at, updated_at FROM $table_name WHERE job_id = %s",
            $job_id
        ));

        if (!$job) {
            return [
                'status' => 'not_found',
                'message' => __('Job not found', 'ai-auto-blog')
            ];
        }

        $result = [
            'status' => $job->status,
            'created_at' => $job->created_at,
            'updated_at' => $job->updated_at
        ];

        switch ($job->status) {
            case 'initiated':
                $result['message'] = __('Job initiated', 'ai-auto-blog');
                break;
            case 'ai_processing':
                $result['message'] = __('AI is generating content...', 'ai-auto-blog');
                break;
            case 'creating_post':
                $result['message'] = __('Creating WordPress post...', 'ai-auto-blog');
                break;
            case 'completed':
                $result['message'] = __('Post created successfully!', 'ai-auto-blog');
                if ($job->result_data) {
                    $result['result'] = json_decode($job->result_data, true);
                }
                break;
            case 'failed':
                $result['message'] = __('Job failed: ', 'ai-auto-blog') . $job->error_message;
                break;
            case 'timeout':
                $result['message'] = __('Job timed out', 'ai-auto-blog');
                break;
            default:
                $result['message'] = __('Unknown status', 'ai-auto-blog');
        }

        return $result;
    }

    private static function create_wordpress_post($settings, $content) {
        preg_match('/<h1[^>]*>(.*?)<\/h1>/i', $content['content'], $matches);
        $title = $matches[1] ?? __('$title', 'ai-auto-blog');
        
        $cleaned_content = wp_kses_post($content['content']);
        
        ai_auto_blog_log("INFO: Preparing post data - Title: " . $title);
        
        $post_data = [
            'title' => sanitize_text_field($title),
            'content' => $cleaned_content,
            'status' => 'publish',
            'categories' => [intval($settings['category'])],
            'meta' => [
                'ai_generated' => true,
                'ai_prompt' => $settings['prompt']
            ]
        ];

        $api_url = trailingslashit($settings['site_url']) . 'wp-json/wp/v2/posts';
        ai_auto_blog_log("INFO: WordPress API URL: " . $api_url);

        $auth = base64_encode($settings['username'] . ':' . $settings['password']);
        
        $response = wp_remote_post($api_url, [
            'headers' => [
                'Authorization' => "Basic " . $auth,
                'Content-Type' => 'application/json',
                'Accept' => 'application/json'
            ],
            'body' => json_encode($post_data),
            'timeout' => 60,
            'sslverify' => true,
            'data_format' => 'body'
        ]);

        if (is_wp_error($response)) {
            $error_message = $response->get_error_message();
            ai_auto_blog_log("ERROR: WordPress API error - " . $error_message);
            throw new Exception(__('WordPress API error: ', 'ai-auto-blog') . $error_message);
        }

        $response_code = wp_remote_retrieve_response_code($response);
        $response_body = wp_remote_retrieve_body($response);
        
        ai_auto_blog_log("INFO: WordPress API Response Code: " . $response_code);
        ai_auto_blog_log("INFO: WordPress API Response Body: " . substr($response_body, 0, 500));

        if ($response_code !== 201) {
            $error_message = sprintf(
                __('Failed to create post. Status code: %d, Response: %s', 'ai-auto-blog'),
                $response_code,
                $response_body
            );
            ai_auto_blog_log("ERROR: " . $error_message);
            throw new Exception($error_message);
        }

        $response_data = json_decode($response_body, true);
        
        if (!isset($response_data['id'])) {
            ai_auto_blog_log("ERROR: No post ID in response - " . print_r($response_data, true));
            throw new Exception(__('Failed to get post ID from response', 'ai-auto-blog'));
        }

        ai_auto_blog_log("SUCCESS: Post created with ID: " . $response_data['id']);
        return $response_data['id'];
    }

    private static function attach_featured_image($post_id, $image_url) {
        try {
            if (empty($image_url)) {
                ai_auto_blog_log("INFO: No image URL provided for featured image");
                return;
            }

            ai_auto_blog_log("INFO: Attempting to attach featured image from URL: " . $image_url);
            
            $image_id = media_sideload_image($image_url, $post_id, __('AI Generated Featured Image', 'ai-auto-blog'), 'id');
            
            if (is_wp_error($image_id)) {
                throw new Exception($image_id->get_error_message());
            }

            $result = set_post_thumbnail($post_id, $image_id);
            
            if ($result) {
                ai_auto_blog_log("SUCCESS: Featured image attached - ID {$image_id}");
            } else {
                throw new Exception("Failed to set post thumbnail");
            }

        } catch (Exception $e) {
            ai_auto_blog_log("WARNING: Failed to attach featured image - " . $e->getMessage());
        }
    }
}

function ai_auto_blog_fetch_and_post() {
    return AI_Auto_Blog_Poster::fetch_and_post();
}

function ai_auto_blog_start_async_generation() {
    return AI_Auto_Blog_Poster::start_async_post_generation();
}

function ai_auto_blog_get_job_status($job_id) {
    return AI_Auto_Blog_Poster::get_job_status($job_id);
}

// Hook for background polling
add_action('ai_auto_blog_poll_job', [AI_Auto_Blog_Poster::class, 'poll_job_status']);

