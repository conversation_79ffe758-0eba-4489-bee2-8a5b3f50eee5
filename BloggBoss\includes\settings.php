<?php
if (!defined('ABSPATH')) exit;

function ai_auto_blog_add_menu() {
    $menu_icon = 'dashicons-edit';
    
    add_menu_page(
        __('AI Auto Blog Settings', 'ai-auto-blog'),
        __('AI Auto Blog', 'ai-auto-blog'),
        'manage_options',
        'ai-auto-blog',
        'ai_auto_blog_settings_page',
        $menu_icon,
        25
    );

    add_submenu_page(
        'ai-auto-blog',
        __('Fetch AI Blog', 'ai-auto-blog'),
        __('Fetch Blog', 'ai-auto-blog'),
        'manage_options',
        'ai-auto-blog-fetch',
        'ai_auto_blog_fetch_and_post_button'
    );

    add_submenu_page(
        'ai-auto-blog',
        __('Logs', 'ai-auto-blog'),
        __('Logs', 'ai-auto-blog'),
        'manage_options',
        'ai-auto-blog-logs',
        'ai_auto_blog_display_logs'
    );
}
add_action('admin_menu', 'ai_auto_blog_add_menu');

function ai_auto_blog_settings_page() {
    // Force refresh schedule status if settings were just saved
    if (isset($_GET['settings-updated']) && $_GET['settings-updated'] == 'true') {
        require_once plugin_dir_path(__FILE__) . 'scheduler.php';
        AI_Auto_Blog_Scheduler::force_reschedule();
    }
    
    ?>
    <div class="wrap">
        <h1><?php _e('AI Auto Blog Settings', 'ai-auto-blog'); ?></h1>
        <form method="post" action="options.php">
            <?php
            settings_fields('ai_auto_blog_options');
            do_settings_sections('ai-auto-blog');
            submit_button();
            ?>
        </form>
        
        <div class="card">
            <h2><?php _e('Automation Status', 'ai-auto-blog'); ?></h2>
            <?php
            $next_run = wp_next_scheduled('ai_auto_blog_daily_post');
            $is_enabled = get_option('ai_auto_blog_automation_enabled', 1);
            ?>
            
            <p>
                <?php _e('Status:', 'ai-auto-blog'); ?> 
                <strong><?php echo $is_enabled ? __('Enabled', 'ai-auto-blog') : __('Disabled', 'ai-auto-blog'); ?></strong>
            </p>
            
            <p>
                <?php _e('Next scheduled post:', 'ai-auto-blog'); ?> 
                <strong>
                    <?php
                    echo $next_run && $is_enabled ? date_i18n(get_option('date_format') . ' ' . get_option('time_format'), $next_run) : __('Not scheduled', 'ai-auto-blog');
                    ?>
                </strong>
            </p>
            
            <?php if ($is_enabled && $next_run): ?>
            <p>
                <?php _e('Current time:', 'ai-auto-blog'); ?> 
                <strong><?php echo date_i18n(get_option('date_format') . ' ' . get_option('time_format'), current_time('timestamp')); ?></strong>
            </p>
            <?php endif; ?>
            
            <?php if (isset($_GET['settings-updated']) && $_GET['settings-updated'] == 'true'): ?>
            <div class="notice notice-success inline">
                <p><?php _e('Settings saved and schedule updated!', 'ai-auto-blog'); ?></p>
            </div>
            <?php endif; ?>
            
            <p>
                <a href="<?php echo esc_url(add_query_arg(['page' => 'ai-auto-blog', 'action' => 'refresh-schedule', '_wpnonce' => wp_create_nonce('refresh_schedule')])); ?>" class="button button-secondary">
                    <?php _e('Refresh Schedule', 'ai-auto-blog'); ?>
                </a>
            </p>
        </div>
    </div>
    <?php
}

// Handle manual schedule refresh
add_action('admin_init', function() {
    if (isset($_GET['action']) && $_GET['action'] === 'refresh-schedule' && isset($_GET['page']) && $_GET['page'] === 'ai-auto-blog') {
        if (!wp_verify_nonce($_GET['_wpnonce'], 'refresh_schedule')) {
            wp_die(__('Security check failed', 'ai-auto-blog'));
        }
        
        require_once plugin_dir_path(__FILE__) . 'scheduler.php';
        AI_Auto_Blog_Scheduler::force_reschedule();
        
        wp_redirect(admin_url('admin.php?page=ai-auto-blog&schedule-refreshed=true'));
        exit;
    }
});

function ai_auto_blog_register_settings() {
    register_setting('ai_auto_blog_options', 'ai_auto_blog_site_url');
    register_setting('ai_auto_blog_options', 'ai_auto_blog_username');
    register_setting('ai_auto_blog_options', 'ai_auto_blog_password');
    register_setting('ai_auto_blog_options', 'ai_auto_blog_category');
    register_setting('ai_auto_blog_options', 'ai_auto_blog_agent_url');
    register_setting('ai_auto_blog_options', 'ai_auto_blog_prompt');
    register_setting('ai_auto_blog_options', 'ai_auto_blog_schedule_time');
    register_setting('ai_auto_blog_options', 'ai_auto_blog_schedule_frequency');
    register_setting('ai_auto_blog_options', 'ai_auto_blog_automation_enabled');
    register_setting('ai_auto_blog_options', 'ai_auto_blog_api_timeout');
    register_setting('ai_auto_blog_options', 'ai_auto_blog_fast_mode');

    add_settings_section(
        'ai_auto_blog_main',
        __('API Configuration', 'ai-auto-blog'),
        null,
        'ai-auto-blog'
    );

    add_settings_section(
        'ai_auto_blog_scheduler',
        __('Posting Schedule', 'ai-auto-blog'),
        null,
        'ai-auto-blog'
    );

    ai_auto_blog_add_settings_fields();
}
add_action('admin_init', 'ai_auto_blog_register_settings');

function ai_auto_blog_add_settings_fields() {
    add_settings_field(
        'ai_auto_blog_site_url',
        __('WordPress Site URL', 'ai-auto-blog'),
        'ai_auto_blog_site_url_callback',
        'ai-auto-blog',
        'ai_auto_blog_main'
    );

    add_settings_field(
        'ai_auto_blog_username',
        __('Admin Username', 'ai-auto-blog'),
        'ai_auto_blog_username_callback',
        'ai-auto-blog',
        'ai_auto_blog_main'
    );

    add_settings_field(
        'ai_auto_blog_password',
        __('Application Password', 'ai-auto-blog'),
        'ai_auto_blog_password_callback',
        'ai-auto-blog',
        'ai_auto_blog_main'
    );

    add_settings_field(
        'ai_auto_blog_category',
        __('Post Category', 'ai-auto-blog'),
        'ai_auto_blog_category_callback',
        'ai-auto-blog',
        'ai_auto_blog_main'
    );

    add_settings_field(
        'ai_auto_blog_agent_url',
        __('AI Agent URL', 'ai-auto-blog'),
        'ai_auto_blog_agent_url_callback',
        'ai-auto-blog',
        'ai_auto_blog_main'
    );

    add_settings_field(
        'ai_auto_blog_prompt',
        __('AI Prompt', 'ai-auto-blog'),
        'ai_auto_blog_prompt_callback',
        'ai-auto-blog',
        'ai_auto_blog_main'
    );

    add_settings_field(
        'ai_auto_blog_api_timeout',
        __('API Timeout (seconds)', 'ai-auto-blog'),
        'ai_auto_blog_api_timeout_callback',
        'ai-auto-blog',
        'ai_auto_blog_main'
    );

    add_settings_field(
        'ai_auto_blog_fast_mode',
        __('Fast Mode (Nginx Fix)', 'ai-auto-blog'),
        'ai_auto_blog_fast_mode_callback',
        'ai-auto-blog',
        'ai_auto_blog_main'
    );

    add_settings_field(
        'ai_auto_blog_schedule_time',
        __('Posting Time', 'ai-auto-blog'),
        'ai_auto_blog_schedule_time_callback',
        'ai-auto-blog',
        'ai_auto_blog_scheduler'
    );

    add_settings_field(
        'ai_auto_blog_schedule_frequency',
        __('Frequency', 'ai-auto-blog'),
        'ai_auto_blog_schedule_frequency_callback',
        'ai-auto-blog',
        'ai_auto_blog_scheduler'
    );

    add_settings_field(
        'ai_auto_blog_automation_enabled',
        __('Automation', 'ai-auto-blog'),
        'ai_auto_blog_automation_callback',
        'ai-auto-blog',
        'ai_auto_blog_scheduler'
    );
}

function ai_auto_blog_site_url_callback() {
    $value = esc_url(get_option('ai_auto_blog_site_url', ''));
    echo '<input type="url" name="ai_auto_blog_site_url" value="' . $value . '" 
          class="regular-text" placeholder="https://yourwebsite.com" required>';
}

function ai_auto_blog_username_callback() {
    $value = esc_attr(get_option('ai_auto_blog_username', ''));
    echo '<input type="text" name="ai_auto_blog_username" value="' . $value . '" 
          class="regular-text" placeholder="admin" required>';
}

function ai_auto_blog_password_callback() {
    $value = esc_attr(get_option('ai_auto_blog_password', ''));
    echo '<input type="password" name="ai_auto_blog_password" value="' . $value . '" 
          class="regular-text" placeholder="••••••••" required>';
}

function ai_auto_blog_category_callback() {
    $categories = get_categories(['hide_empty' => false]);
    $current = get_option('ai_auto_blog_category', '');
    
    echo '<select name="ai_auto_blog_category" class="regular-text">';
    echo '<option value="">' . esc_html__('Select a category', 'ai-auto-blog') . '</option>';
    foreach ($categories as $category) {
        printf(
            '<option value="%s" %s>%s (ID: %s)</option>',
            esc_attr($category->term_id),
            selected($current, $category->term_id, false),
            esc_html($category->name),
            esc_html($category->term_id)
        );
    }
    echo '</select>';
}

function ai_auto_blog_agent_url_callback() {
    $value = esc_attr(get_option('ai_auto_blog_agent_url', ''));
    echo '<input type="text" name="ai_auto_blog_agent_url" value="' . $value . '" 
          class="regular-text" placeholder="chatflow-id" required>';
}

function ai_auto_blog_prompt_callback() {
    $value = esc_textarea(get_option('ai_auto_blog_prompt', ''));
    echo '<textarea name="ai_auto_blog_prompt" rows="5" class="large-text"
          placeholder="Write your prompt here..." required>' . $value . '</textarea>';
}

function ai_auto_blog_api_timeout_callback() {
    $value = intval(get_option('ai_auto_blog_api_timeout', 300));
    echo '<input type="number" name="ai_auto_blog_api_timeout" value="' . $value . '"
          class="small-text" min="60" max="600" step="30">';
    echo '<p class="description">' . __('Timeout for AI API requests in seconds (60-600). Default: 300 (5 minutes)', 'ai-auto-blog') . '</p>';
}

function ai_auto_blog_fast_mode_callback() {
    $enabled = get_option('ai_auto_blog_fast_mode', 1);
    echo '<label><input type="checkbox" name="ai_auto_blog_fast_mode" value="1" '
       . checked(1, $enabled, false) . '> ' . __('Enable fast mode (shorter content, 50s timeout)', 'ai-auto-blog') . '</label>';
    echo '<p class="description">' . __('Temporary workaround for 504 Gateway Timeout errors. Generates shorter content that processes faster.', 'ai-auto-blog') . '</p>';
}

function ai_auto_blog_schedule_time_callback() {
    $value = esc_attr(get_option('ai_auto_blog_schedule_time', '09:00'));
    echo '<input type="time" name="ai_auto_blog_schedule_time" value="' . $value . '" 
          class="time-picker" required>';
    echo '<p class="description">' . __('Enter time in 24-hour format (HH:MM)', 'ai-auto-blog') . '</p>';
}

function ai_auto_blog_schedule_frequency_callback() {
    $value = get_option('ai_auto_blog_schedule_frequency', 'daily');
    echo '<select name="ai_auto_blog_schedule_frequency" class="regular-text">
            <option value="daily" ' . selected($value, 'daily', false) . '>' . __('Daily', 'ai-auto-blog') . '</option>
            <option value="weekly" ' . selected($value, 'weekly', false) . '>' . __('Weekly', 'ai-auto-blog') . '</option>
          </select>';
}

function ai_auto_blog_automation_callback() {
    $enabled = get_option('ai_auto_blog_automation_enabled', 1);
    echo '<label><input type="checkbox" name="ai_auto_blog_automation_enabled" value="1" ' 
       . checked(1, $enabled, false) . '> ' . __('Enable automatic posting', 'ai-auto-blog') . '</label>';
}

function ai_auto_blog_fetch_and_post_button() {
    $response = '';

    // Handle async post generation
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['ai_auto_blog_async_post'])) {
        if (!wp_verify_nonce($_POST['_wpnonce'], 'ai_auto_blog_fetch')) {
            $response = '<div class="notice notice-error"><p>' . __('Security verification failed!', 'ai-auto-blog') . '</p></div>';
        } else {
            require_once plugin_dir_path(__FILE__) . 'post-handler.php';
            $result = ai_auto_blog_start_async_generation();

            if ($result['status'] === 'success') {
                $response = '<div class="notice notice-success"><p>' . $result['message'] . '</p>';
                $response .= '<p><strong>Job ID:</strong> ' . $result['job_id'] . '</p>';
                $response .= '<p><a href="#" onclick="checkJobStatus(\'' . $result['job_id'] . '\'); return false;" class="button">Check Status</a></p>';
                $response .= '</div>';

                // Store job ID in session for status checking
                if (!session_id()) session_start();
                $_SESSION['last_job_id'] = $result['job_id'];
            } else {
                $response = '<div class="notice notice-error"><p>' . $result['message'] . '</p></div>';
            }
        }
    }

    // Handle synchronous post generation (original)
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['ai_auto_blog_fetch_post'])) {
        if (!wp_verify_nonce($_POST['_wpnonce'], 'ai_auto_blog_fetch')) {
            $response = '<div class="notice notice-error"><p>' . __('Security verification failed!', 'ai-auto-blog') . '</p></div>';
        } else {
            require_once plugin_dir_path(__FILE__) . 'post-handler.php';
            $result = ai_auto_blog_fetch_and_post();
            $response = '<div class="notice notice-' . ($result['status'] === 'success' ? 'success' : 'error') . '"><pre>'
                      . esc_html(print_r($result, true)) . '</pre></div>';
        }
    }

    // Handle job status check
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['check_job_status']) && isset($_POST['job_id'])) {
        if (!wp_verify_nonce($_POST['_wpnonce'], 'ai_auto_blog_fetch')) {
            $response = '<div class="notice notice-error"><p>' . __('Security verification failed!', 'ai-auto-blog') . '</p></div>';
        } else {
            require_once plugin_dir_path(__FILE__) . 'post-handler.php';
            $status = ai_auto_blog_get_job_status($_POST['job_id']);

            $status_class = ($status['status'] === 'completed') ? 'notice-success' :
                           (($status['status'] === 'failed' || $status['status'] === 'timeout') ? 'notice-error' : 'notice-info');

            $response = '<div class="notice ' . $status_class . '"><p><strong>Job Status:</strong> ' . $status['message'] . '</p>';

            if (isset($status['result']) && isset($status['result']['post_url'])) {
                $response .= '<p><a href="' . $status['result']['post_url'] . '" target="_blank" class="button button-primary">View Post</a></p>';
            }

            $response .= '</div>';
        }
    }

    ?>
    <div class="wrap">
        <h1><?php _e('Manual Post Generation', 'ai-auto-blog'); ?></h1>

        <?php echo $response; ?>

        <div style="margin-bottom: 20px;">
            <h3><?php _e('Asynchronous Generation (Recommended)', 'ai-auto-blog'); ?></h3>
            <p><?php _e('This method starts the generation process and returns immediately. You can check the status and will be notified when complete.', 'ai-auto-blog'); ?></p>

            <form method="post" style="display: inline-block; margin-right: 10px;">
                <?php wp_nonce_field('ai_auto_blog_fetch', '_wpnonce'); ?>
                <p class="submit">
                    <button type="submit" name="ai_auto_blog_async_post" class="button button-primary">
                        <?php _e('Start Async Generation', 'ai-auto-blog'); ?>
                    </button>
                </p>
            </form>

            <?php if (!session_id()) session_start(); ?>
            <?php if (isset($_SESSION['last_job_id'])): ?>
            <form method="post" style="display: inline-block;">
                <?php wp_nonce_field('ai_auto_blog_fetch', '_wpnonce'); ?>
                <input type="hidden" name="job_id" value="<?php echo esc_attr($_SESSION['last_job_id']); ?>">
                <p class="submit">
                    <button type="submit" name="check_job_status" class="button">
                        <?php _e('Check Last Job Status', 'ai-auto-blog'); ?>
                    </button>
                </p>
            </form>
            <?php endif; ?>
        </div>

        <div style="border-top: 1px solid #ccc; padding-top: 20px;">
            <h3><?php _e('Synchronous Generation (Legacy)', 'ai-auto-blog'); ?></h3>
            <p><?php _e('This method waits for the entire process to complete. May timeout for long-running AI processes.', 'ai-auto-blog'); ?></p>

            <form method="post">
                <?php wp_nonce_field('ai_auto_blog_fetch', '_wpnonce'); ?>
                <p class="submit">
                    <button type="submit" name="ai_auto_blog_fetch_post" class="button">
                        <?php _e('Generate Post Now (Sync)', 'ai-auto-blog'); ?>
                    </button>
                </p>
            </form>
        </div>
    </div>

    <script>
    function checkJobStatus(jobId) {
        var statusDiv = document.getElementById('job-status-' + jobId);
        if (!statusDiv) {
            // Create status div if it doesn't exist
            statusDiv = document.createElement('div');
            statusDiv.id = 'job-status-' + jobId;
            statusDiv.style.marginTop = '10px';
            statusDiv.style.padding = '10px';
            statusDiv.style.border = '1px solid #ccc';
            statusDiv.style.backgroundColor = '#f9f9f9';
            document.querySelector('.wrap').appendChild(statusDiv);
        }

        statusDiv.innerHTML = '<p>Checking status...</p>';

        // Use AJAX to check status
        var xhr = new XMLHttpRequest();
        xhr.open('POST', '<?php echo admin_url('admin-ajax.php'); ?>', true);
        xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');

        xhr.onreadystatechange = function() {
            if (xhr.readyState === 4) {
                if (xhr.status === 200) {
                    try {
                        var response = JSON.parse(xhr.responseText);
                        if (response.success) {
                            var status = response.data;
                            var statusClass = '';
                            var statusColor = '';

                            switch(status.status) {
                                case 'completed':
                                    statusClass = 'notice-success';
                                    statusColor = 'green';
                                    break;
                                case 'failed':
                                case 'timeout':
                                    statusClass = 'notice-error';
                                    statusColor = 'red';
                                    break;
                                case 'ai_complete':
                                case 'creating_post':
                                    statusClass = 'notice-info';
                                    statusColor = 'orange';
                                    break;
                                default:
                                    statusClass = 'notice-info';
                                    statusColor = 'blue';
                            }

                            var html = '<div class="notice ' + statusClass + '" style="margin: 0;">';
                            html += '<p><strong>Status:</strong> <span style="color: ' + statusColor + '">' + status.message + '</span></p>';
                            html += '<p><strong>Last Updated:</strong> ' + status.updated_at + '</p>';

                            if (status.result && status.result.post_url) {
                                html += '<p><a href="' + status.result.post_url + '" target="_blank" class="button button-primary">View Post</a></p>';
                            }

                            if (status.status === 'ai_processing' || status.status === 'ai_complete' || status.status === 'creating_post') {
                                html += '<p><button onclick="checkJobStatus(\'' + jobId + '\')" class="button">Refresh Status</button></p>';
                                // Auto-refresh every 15 seconds for active jobs
                                setTimeout(function() {
                                    checkJobStatus(jobId);
                                }, 15000);
                            }

                            html += '</div>';
                            statusDiv.innerHTML = html;
                        } else {
                            statusDiv.innerHTML = '<div class="notice notice-error"><p>Error: ' + response.data + '</p></div>';
                        }
                    } catch (e) {
                        statusDiv.innerHTML = '<div class="notice notice-error"><p>Error parsing response</p></div>';
                    }
                } else {
                    statusDiv.innerHTML = '<div class="notice notice-error"><p>Request failed</p></div>';
                }
            }
        };

        var params = 'action=ai_auto_blog_check_status&job_id=' + encodeURIComponent(jobId) + '&nonce=<?php echo wp_create_nonce('ai_auto_blog_status_nonce'); ?>';
        xhr.send(params);
    }
    </script>
    <?php
}

function ai_auto_blog_display_logs() {
    $log_file = plugin_dir_path(__FILE__) . '../logs.txt';
    $log_entries = [];

    if (file_exists($log_file) && is_readable($log_file)) {
        $log_content = file_get_contents($log_file);
        $lines = array_filter(explode("\n", $log_content));

        foreach ($lines as $line) {
            if (preg_match('/^\[([^\]]+)\]\s+(\w+):\s*(.+)$/', $line, $matches)) {
                $log_entries[] = [
                    'timestamp' => $matches[1],
                    'level' => $matches[2],
                    'message' => $matches[3],
                    'raw' => $line
                ];
            } else if (!empty(trim($line))) {
                // Handle lines that don't match the pattern
                $log_entries[] = [
                    'timestamp' => '',
                    'level' => 'INFO',
                    'message' => $line,
                    'raw' => $line
                ];
            }
        }

        // Reverse to show newest first
        $log_entries = array_reverse($log_entries);
    }

    // Get filter parameters
    $filter_level = isset($_GET['filter_level']) ? sanitize_text_field($_GET['filter_level']) : '';
    $search_term = isset($_GET['search']) ? sanitize_text_field($_GET['search']) : '';

    // Apply filters
    if (!empty($filter_level) || !empty($search_term)) {
        $log_entries = array_filter($log_entries, function($entry) use ($filter_level, $search_term) {
            $level_match = empty($filter_level) || $entry['level'] === $filter_level;
            $search_match = empty($search_term) || stripos($entry['message'], $search_term) !== false;
            return $level_match && $search_match;
        });
    }

    ?>
    <div class="wrap">
        <h1><?php _e('System Logs', 'ai-auto-blog'); ?></h1>

        <style>
        .log-container {
            background: #fff;
            border: 1px solid #ccd0d4;
            border-radius: 4px;
            margin-top: 20px;
        }

        .log-header {
            background: #f6f7f7;
            border-bottom: 1px solid #ccd0d4;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 15px;
        }

        .log-filters {
            display: flex;
            gap: 10px;
            align-items: center;
            flex-wrap: wrap;
        }

        .log-filters input, .log-filters select {
            padding: 5px 10px;
            border: 1px solid #ddd;
            border-radius: 3px;
        }

        .log-actions {
            display: flex;
            gap: 10px;
        }

        .log-entries {
            max-height: 600px;
            overflow-y: auto;
        }

        .log-entry {
            padding: 12px 20px;
            border-bottom: 1px solid #f0f0f1;
            display: flex;
            align-items: flex-start;
            gap: 15px;
        }

        .log-entry:last-child {
            border-bottom: none;
        }

        .log-entry:hover {
            background: #f9f9f9;
        }

        .log-timestamp {
            font-family: monospace;
            font-size: 12px;
            color: #666;
            white-space: nowrap;
            min-width: 140px;
        }

        .log-level {
            font-weight: bold;
            font-size: 11px;
            padding: 2px 8px;
            border-radius: 3px;
            text-transform: uppercase;
            min-width: 60px;
            text-align: center;
        }

        .log-level.ERROR {
            background: #dc3232;
            color: white;
        }

        .log-level.WARNING {
            background: #ffb900;
            color: white;
        }

        .log-level.INFO {
            background: #00a0d2;
            color: white;
        }

        .log-level.SUCCESS {
            background: #46b450;
            color: white;
        }

        .log-message {
            flex: 1;
            font-family: monospace;
            font-size: 13px;
            line-height: 1.4;
            word-break: break-word;
        }

        .log-stats {
            display: flex;
            gap: 20px;
            margin-bottom: 10px;
            font-size: 13px;
        }

        .log-stat {
            padding: 5px 10px;
            background: #f0f0f1;
            border-radius: 3px;
        }

        .no-logs {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .refresh-btn {
            background: #0073aa !important;
            color: white !important;
            border-color: #0073aa !important;
        }

        .auto-refresh {
            font-size: 12px;
            color: #666;
        }
        </style>

        <div class="log-container">
            <div class="log-header">
                <div class="log-filters">
                    <form method="get" style="display: flex; gap: 10px; align-items: center;">
                        <input type="hidden" name="page" value="ai-auto-blog-logs">

                        <select name="filter_level">
                            <option value=""><?php _e('All Levels', 'ai-auto-blog'); ?></option>
                            <option value="ERROR" <?php selected($filter_level, 'ERROR'); ?>><?php _e('Errors', 'ai-auto-blog'); ?></option>
                            <option value="WARNING" <?php selected($filter_level, 'WARNING'); ?>><?php _e('Warnings', 'ai-auto-blog'); ?></option>
                            <option value="INFO" <?php selected($filter_level, 'INFO'); ?>><?php _e('Info', 'ai-auto-blog'); ?></option>
                            <option value="SUCCESS" <?php selected($filter_level, 'SUCCESS'); ?>><?php _e('Success', 'ai-auto-blog'); ?></option>
                        </select>

                        <input type="text" name="search" value="<?php echo esc_attr($search_term); ?>"
                               placeholder="<?php _e('Search logs...', 'ai-auto-blog'); ?>">

                        <button type="submit" class="button"><?php _e('Filter', 'ai-auto-blog'); ?></button>

                        <?php if (!empty($filter_level) || !empty($search_term)): ?>
                            <a href="<?php echo admin_url('admin.php?page=ai-auto-blog-logs'); ?>" class="button">
                                <?php _e('Clear Filters', 'ai-auto-blog'); ?>
                            </a>
                        <?php endif; ?>
                    </form>
                </div>

                <div class="log-actions">
                    <button onclick="location.reload()" class="button refresh-btn">
                        <?php _e('Refresh', 'ai-auto-blog'); ?>
                    </button>
                    <a href="<?php echo wp_nonce_url(admin_url('admin.php?page=ai-auto-blog-logs&action=clear-logs'), 'clear_logs'); ?>"
                       class="button" onclick="return confirm('<?php _e('Are you sure you want to clear all logs?', 'ai-auto-blog'); ?>')">
                        <?php _e('Clear Logs', 'ai-auto-blog'); ?>
                    </a>
                </div>
            </div>

            <?php if (!empty($log_entries)): ?>
                <?php
                // Calculate stats
                $stats = ['ERROR' => 0, 'WARNING' => 0, 'INFO' => 0, 'SUCCESS' => 0];
                foreach ($log_entries as $entry) {
                    if (isset($stats[$entry['level']])) {
                        $stats[$entry['level']]++;
                    }
                }
                ?>

                <div class="log-stats" style="padding: 15px 20px; border-bottom: 1px solid #f0f0f1;">
                    <div class="log-stat">
                        <strong><?php echo count($log_entries); ?></strong> <?php _e('Total Entries', 'ai-auto-blog'); ?>
                    </div>
                    <?php foreach ($stats as $level => $count): ?>
                        <?php if ($count > 0): ?>
                            <div class="log-stat">
                                <span class="log-level <?php echo $level; ?>" style="margin-right: 5px;"><?php echo $level; ?></span>
                                <strong><?php echo $count; ?></strong>
                            </div>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </div>

                <div class="log-entries">
                    <?php foreach ($log_entries as $entry): ?>
                        <div class="log-entry">
                            <div class="log-timestamp"><?php echo esc_html($entry['timestamp']); ?></div>
                            <div class="log-level <?php echo esc_attr($entry['level']); ?>">
                                <?php echo esc_html($entry['level']); ?>
                            </div>
                            <div class="log-message"><?php echo esc_html($entry['message']); ?></div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php else: ?>
                <div class="no-logs">
                    <p><?php _e('No log entries found.', 'ai-auto-blog'); ?></p>
                    <?php if (!empty($filter_level) || !empty($search_term)): ?>
                        <p><?php _e('Try adjusting your filters or', 'ai-auto-blog'); ?>
                           <a href="<?php echo admin_url('admin.php?page=ai-auto-blog-logs'); ?>">
                               <?php _e('view all logs', 'ai-auto-blog'); ?>
                           </a>
                        </p>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
        </div>

        <script>
        // Auto-refresh every 30 seconds if on logs page
        setInterval(function() {
            if (document.querySelector('.log-container')) {
                // Only refresh if no filters are applied to avoid losing user's filter state
                const urlParams = new URLSearchParams(window.location.search);
                if (!urlParams.get('filter_level') && !urlParams.get('search')) {
                    location.reload();
                }
            }
        }, 30000);
        </script>
    </div>
    <?php
}

add_action('admin_init', function() {
    if (isset($_GET['action']) && $_GET['action'] === 'clear-logs' && isset($_GET['page']) && $_GET['page'] === 'ai-auto-blog-logs') {
        if (!wp_verify_nonce($_GET['_wpnonce'], 'clear_logs')) {
            wp_die(__('Security check failed', 'ai-auto-blog'));
        }
        
        $log_file = plugin_dir_path(__FILE__) . '../logs.txt';
        if (file_exists($log_file)) {
            file_put_contents($log_file, '');
        }
        
        wp_redirect(admin_url('admin.php?page=ai-auto-blog-logs'));
        exit;
    }
});