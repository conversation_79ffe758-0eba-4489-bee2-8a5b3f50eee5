<?php
/**
 * Plugin Name: Blog Boss
 * Plugin URI: https://bloggboss.com
 * Description: Automatically posts AI-generated blogs to a user's WordPress site.
 * Version: 1.0
 * Author: Critical Future
 * Author URI: https://criticalfutureglobal.com
 * License: GPL2
 */

if (!defined('ABSPATH')) exit;

require_once plugin_dir_path(__FILE__) . 'includes/settings.php';
require_once plugin_dir_path(__FILE__) . 'includes/scheduler.php';
require_once plugin_dir_path(__FILE__) . 'includes/post-handler.php';
require_once plugin_dir_path(__FILE__) . 'includes/logs.php';
require_once plugin_dir_path(__FILE__) . 'includes/license.php';
require_once plugin_dir_path(__FILE__) . 'includes/diagnostics.php';

function ai_auto_blog_activate() {
    AI_Auto_Blog_Scheduler::activate();

    // Create the jobs table for async processing
    require_once plugin_dir_path(__FILE__) . 'includes/post-handler.php';
    AI_Auto_Blog_Poster::create_jobs_table_public();
}
register_activation_hook(__FILE__, 'ai_auto_blog_activate');

function ai_auto_blog_deactivate() {
    AI_Auto_Blog_Scheduler::deactivate();
}
register_deactivation_hook(__FILE__, 'ai_auto_blog_deactivate');

/**
 * Hook the scheduled event to the function that fetches and posts AI blogs.
 */
add_action('ai_auto_blog_daily_post', 'ai_auto_blog_fetch_and_post');

/**
 * AJAX endpoint for checking job status
 */
add_action('wp_ajax_ai_auto_blog_check_status', 'ai_auto_blog_ajax_check_status');

function ai_auto_blog_ajax_check_status() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'ai_auto_blog_status_nonce')) {
        wp_die('Security check failed');
    }

    if (!isset($_POST['job_id'])) {
        wp_send_json_error('No job ID provided');
    }

    require_once plugin_dir_path(__FILE__) . 'includes/post-handler.php';
    $status = ai_auto_blog_get_job_status($_POST['job_id']);

    wp_send_json_success($status);
}